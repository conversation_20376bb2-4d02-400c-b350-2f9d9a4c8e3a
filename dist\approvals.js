/**
 * Command Approval System
 *
 * Handles command approval workflow with different policies,
 * safety checks, and user interaction for command execution.
 */
import { EventEmitter } from 'events';
import { resolve, relative } from 'path';
import { existsSync, accessSync, constants } from 'fs';
import { formatCommandForDisplay, isDangerousCommand, getCommandCategory } from './format-command.js';
import { logInfo, logWarn } from './utils/logger/log.js';
export var ReviewDecision;
(function (ReviewDecision) {
    ReviewDecision["YES"] = "yes";
    ReviewDecision["NO_CONTINUE"] = "no_continue";
    ReviewDecision["NO_EXIT"] = "no_exit";
    ReviewDecision["ALWAYS"] = "always";
    ReviewDecision["EXPLAIN"] = "explain";
})(ReviewDecision || (ReviewDecision = {}));
class ApprovalManager extends EventEmitter {
    config;
    alwaysApprovedCommands = new Set();
    pendingRequests = new Map();
    constructor(config = {}) {
        super();
        this.config = {
            policy: 'suggest',
            safeCommands: [
                'ls', 'dir', 'cat', 'head', 'tail', 'grep', 'find', 'locate',
                'pwd', 'whoami', 'date', 'echo', 'which', 'where',
                'git status', 'git log', 'git diff', 'git show',
                'npm list', 'npm info', 'yarn info',
                'ps', 'top', 'df', 'du', 'free', 'uptime',
            ],
            dangerousCommands: [
                'rm', 'rmdir', 'del', 'rd', 'format', 'fdisk',
                'sudo', 'su', 'chmod', 'chown', 'chgrp',
                'kill', 'killall', 'pkill', 'shutdown', 'reboot',
                'dd', 'shred', 'wipe', 'mkfs',
            ],
            allowedPaths: [],
            blockedPaths: [
                '/etc', '/usr/bin', '/usr/sbin', '/bin', '/sbin',
                'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
            ],
            autoApprovePatterns: [],
            requireExplanation: false,
            ...config,
        };
    }
    /**
     * Check if command can be auto-approved
     */
    canAutoApprove(execInput) {
        const { command, workdir } = execInput;
        if (this.config.policy === 'suggest') {
            return false;
        }
        if (this.config.policy === 'full-auto') {
            return true;
        }
        // For auto-edit policy, check safety
        return this.isCommandSafe(command, workdir);
    }
    /**
     * Check if command is safe to execute
     */
    isCommandSafe(command, workdir) {
        if (!command || command.length === 0) {
            return false;
        }
        const commandStr = formatCommandForDisplay(command);
        const baseCommand = command[0].toLowerCase();
        const baseName = baseCommand.split('/').pop() || baseCommand;
        // Check if command is in always approved list
        if (this.alwaysApprovedCommands.has(commandStr)) {
            return true;
        }
        // Check against safe commands list
        if (this.config.safeCommands.some(safe => commandStr.toLowerCase().startsWith(safe.toLowerCase()))) {
            return true;
        }
        // Check against dangerous commands list
        if (this.config.dangerousCommands.includes(baseName)) {
            return false;
        }
        // Check if command is inherently dangerous
        if (isDangerousCommand(command)) {
            return false;
        }
        // Check working directory safety
        if (workdir && !this.isPathSafe(workdir)) {
            return false;
        }
        // Check auto-approve patterns
        if (this.config.autoApprovePatterns.some(pattern => {
            const regex = new RegExp(pattern, 'i');
            return regex.test(commandStr);
        })) {
            return true;
        }
        // Default to requiring approval for unknown commands
        return false;
    }
    /**
     * Check if path is safe for operations
     */
    isPathSafe(path) {
        const resolvedPath = resolve(path);
        // Check against blocked paths
        for (const blockedPath of this.config.blockedPaths) {
            if (resolvedPath.startsWith(resolve(blockedPath))) {
                return false;
            }
        }
        // Check against allowed paths (if specified)
        if (this.config.allowedPaths.length > 0) {
            return this.config.allowedPaths.some(allowedPath => resolvedPath.startsWith(resolve(allowedPath)));
        }
        // Check if path exists and is accessible
        try {
            if (existsSync(resolvedPath)) {
                accessSync(resolvedPath, constants.R_OK);
                return true;
            }
        }
        catch (_error) {
            return false;
        }
        return true;
    }
    /**
     * Request approval for command execution
     */
    async requestApproval(execInput, explanation) {
        const { command, workdir } = execInput;
        // Check if auto-approval is possible
        if (this.canAutoApprove(execInput)) {
            logInfo(`Auto-approved command: ${formatCommandForDisplay(command)}`);
            return { decision: ReviewDecision.YES };
        }
        // Create approval request
        const request = {
            id: Math.random().toString(36).substr(2, 9),
            command,
            workdir: workdir || process.cwd(),
            explanation,
            category: getCommandCategory(command),
            isDangerous: isDangerousCommand(command),
            timestamp: Date.now(),
        };
        this.pendingRequests.set(request.id, request);
        logInfo(`Requesting approval for command: ${formatCommandForDisplay(command)}`);
        // Emit approval request event
        this.emit('approval-requested', request);
        // Return promise that resolves when approval is given
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                this.pendingRequests.delete(request.id);
                reject(new Error('Approval request timed out'));
            }, 300000); // 5 minute timeout
            const handleApproval = (result) => {
                clearTimeout(timeout);
                this.pendingRequests.delete(request.id);
                // Handle always approve
                if (result.alwaysApprove) {
                    const commandStr = formatCommandForDisplay(command);
                    this.alwaysApprovedCommands.add(commandStr);
                    logInfo(`Command added to always approved: ${commandStr}`);
                }
                resolve(result);
            };
            this.once(`approval-${request.id}`, handleApproval);
        });
    }
    /**
     * Submit approval decision
     */
    submitApproval(requestId, result) {
        const request = this.pendingRequests.get(requestId);
        if (!request) {
            logWarn(`Approval request not found: ${requestId}`);
            return;
        }
        logInfo(`Approval decision for ${formatCommandForDisplay(request.command)}: ${result.decision}`);
        this.emit(`approval-${requestId}`, result);
    }
    /**
     * Get pending approval requests
     */
    getPendingRequests() {
        return Array.from(this.pendingRequests.values());
    }
    /**
     * Cancel approval request
     */
    cancelApproval(requestId, reason) {
        const request = this.pendingRequests.get(requestId);
        if (request) {
            this.pendingRequests.delete(requestId);
            this.emit(`approval-${requestId}`, {
                decision: ReviewDecision.NO_EXIT,
                reason: reason || 'Cancelled',
            });
        }
    }
    /**
     * Update approval configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        logInfo('Approval configuration updated');
    }
    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Clear always approved commands
     */
    clearAlwaysApproved() {
        this.alwaysApprovedCommands.clear();
        logInfo('Cleared always approved commands');
    }
    /**
     * Get always approved commands
     */
    getAlwaysApproved() {
        return Array.from(this.alwaysApprovedCommands);
    }
    /**
     * Validate command against security policies
     */
    validateCommand(execInput) {
        const { command, workdir } = execInput;
        const warnings = [];
        const errors = [];
        // Check command format
        if (!command || command.length === 0) {
            errors.push('Empty command');
            return { valid: false, warnings, errors };
        }
        // Check for dangerous commands
        if (isDangerousCommand(command)) {
            warnings.push('Command is potentially dangerous');
        }
        // Check working directory
        if (workdir) {
            if (!existsSync(workdir)) {
                errors.push(`Working directory does not exist: ${workdir}`);
            }
            else if (!this.isPathSafe(workdir)) {
                warnings.push(`Working directory may be unsafe: ${workdir}`);
            }
        }
        // Check for suspicious patterns
        const commandStr = formatCommandForDisplay(command);
        const suspiciousPatterns = [
            /rm\s+-rf\s+\//, // rm -rf /
            /sudo\s+rm/, // sudo rm
            />\s*\/dev\/null/, // Hiding output
            /curl.*\|\s*sh/, // Pipe to shell
            /wget.*\|\s*sh/, // Pipe to shell
        ];
        for (const pattern of suspiciousPatterns) {
            if (pattern.test(commandStr)) {
                warnings.push('Command contains suspicious patterns');
                break;
            }
        }
        return {
            valid: errors.length === 0,
            warnings,
            errors,
        };
    }
}
// Global approval manager instance
let globalApprovalManager = null;
/**
 * Get or create global approval manager
 */
export function getApprovalManager(config) {
    if (!globalApprovalManager) {
        globalApprovalManager = new ApprovalManager(config);
    }
    return globalApprovalManager;
}
/**
 * Check if command can be auto-approved
 */
export function canAutoApprove(command, approvalPolicy, safeCommands = []) {
    const manager = getApprovalManager({ policy: approvalPolicy, safeCommands });
    return manager.canAutoApprove({ command });
}
/**
 * Request command approval
 */
export async function requestCommandApproval(execInput, approvalPolicy, explanation) {
    const manager = getApprovalManager({ policy: approvalPolicy });
    return manager.requestApproval(execInput, explanation);
}
/**
 * Format approval request for display
 */
export function formatApprovalRequest(request) {
    const lines = [];
    lines.push(`Command: ${formatCommandForDisplay(request.command)}`);
    lines.push(`Working Directory: ${request.workdir}`);
    lines.push(`Category: ${request.category}`);
    if (request.isDangerous) {
        lines.push('⚠️  This command is potentially dangerous');
    }
    if (request.explanation) {
        lines.push(`Explanation: ${request.explanation}`);
    }
    return lines.join('\n');
}
/**
 * Get approval prompt text
 */
export function getApprovalPrompt(request) {
    const commandStr = formatCommandForDisplay(request.command);
    const relativeDir = relative(process.cwd(), request.workdir);
    const dirDisplay = relativeDir || '.';
    let prompt = `Execute command in ${dirDisplay}:\n${commandStr}`;
    if (request.isDangerous) {
        prompt += '\n\n⚠️  WARNING: This command is potentially dangerous!';
    }
    if (request.explanation) {
        prompt += `\n\nExplanation: ${request.explanation}`;
    }
    prompt += '\n\nApprove execution? (y/n/a/e/q)';
    prompt += '\n  y = yes, n = no (continue), a = always, e = explain, q = quit';
    return prompt;
}
/**
 * Parse approval input
 */
export function parseApprovalInput(input) {
    const normalized = input.toLowerCase().trim();
    switch (normalized) {
        case 'y':
        case 'yes':
            return ReviewDecision.YES;
        case 'n':
        case 'no':
            return ReviewDecision.NO_CONTINUE;
        case 'a':
        case 'always':
            return ReviewDecision.ALWAYS;
        case 'e':
        case 'explain':
            return ReviewDecision.EXPLAIN;
        case 'q':
        case 'quit':
        case 'exit':
            return ReviewDecision.NO_EXIT;
        default:
            return ReviewDecision.NO_CONTINUE;
    }
}
/**
 * Create approval configuration from policy
 */
export function createApprovalConfig(policy) {
    const baseConfig = {
        policy,
        safeCommands: [
            'ls', 'dir', 'cat', 'head', 'tail', 'grep', 'find', 'locate',
            'pwd', 'whoami', 'date', 'echo', 'which', 'where',
            'git status', 'git log', 'git diff', 'git show', 'git branch',
            'npm list', 'npm info', 'npm view', 'yarn info',
            'ps', 'top', 'df', 'du', 'free', 'uptime', 'uname',
        ],
        dangerousCommands: [
            'rm', 'rmdir', 'del', 'rd', 'format', 'fdisk',
            'sudo', 'su', 'chmod', 'chown', 'chgrp',
            'kill', 'killall', 'pkill', 'shutdown', 'reboot',
            'dd', 'shred', 'wipe', 'mkfs', 'mount', 'umount',
        ],
        allowedPaths: [],
        blockedPaths: [
            '/etc', '/usr/bin', '/usr/sbin', '/bin', '/sbin', '/root',
            'C:\\Windows', 'C:\\Program Files', 'C:\\Program Files (x86)',
            'C:\\System32', 'C:\\SysWOW64',
        ],
        autoApprovePatterns: [],
        requireExplanation: policy === 'suggest',
    };
    // Adjust configuration based on policy
    switch (policy) {
        case 'full-auto':
            baseConfig.autoApprovePatterns = ['.*']; // Approve everything
            baseConfig.requireExplanation = false;
            break;
        case 'auto-edit':
            baseConfig.autoApprovePatterns = [
                '^(ls|dir|cat|head|tail|grep|find)',
                '^git (status|log|diff|show|branch)',
                '^npm (list|info|view)',
                '^(pwd|whoami|date|echo|which)',
            ];
            break;
        case 'suggest':
        default:
            // Most restrictive - require approval for everything
            break;
    }
    return baseConfig;
}
/**
 * Get security assessment for command
 */
export function getSecurityAssessment(command) {
    const reasons = [];
    const recommendations = [];
    let riskLevel = 'low';
    if (!command || command.length === 0) {
        return { riskLevel: 'critical', reasons: ['Empty command'], recommendations: [] };
    }
    const commandStr = formatCommandForDisplay(command);
    const baseCommand = command[0].toLowerCase();
    const baseName = baseCommand.split('/').pop() || baseCommand;
    // Check for critical risks
    if (['rm', 'del', 'format', 'fdisk', 'dd'].includes(baseName)) {
        riskLevel = 'critical';
        reasons.push('Command can permanently delete data');
        recommendations.push('Ensure you have backups before proceeding');
    }
    if (['sudo', 'su'].includes(baseName)) {
        riskLevel = 'critical';
        reasons.push('Command runs with elevated privileges');
        recommendations.push('Verify the command is necessary and safe');
    }
    // Check for high risks
    if (['chmod', 'chown', 'kill', 'shutdown'].includes(baseName)) {
        riskLevel = Math.max(riskLevel === 'critical' ? 4 : 3, 3) === 4 ? 'critical' : 'high';
        reasons.push('Command can affect system security or stability');
        recommendations.push('Double-check the command parameters');
    }
    // Check for medium risks
    if (['mv', 'cp', 'mkdir', 'rmdir'].includes(baseName)) {
        if (riskLevel === 'low') {
            riskLevel = 'medium';
        }
        reasons.push('Command modifies file system');
        recommendations.push('Verify target paths are correct');
    }
    // Check for suspicious patterns
    if (/\|\s*sh/.test(commandStr) || /\|\s*bash/.test(commandStr)) {
        riskLevel = 'high';
        reasons.push('Command pipes output to shell execution');
        recommendations.push('Review the piped command carefully');
    }
    if (/curl.*\|/.test(commandStr) || /wget.*\|/.test(commandStr)) {
        riskLevel = 'high';
        reasons.push('Command downloads and executes content');
        recommendations.push('Verify the source is trustworthy');
    }
    return { riskLevel, reasons, recommendations };
}
export default ApprovalManager;
//# sourceMappingURL=approvals.js.map