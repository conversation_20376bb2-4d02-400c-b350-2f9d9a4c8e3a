{"version": 3, "file": "use-state.d.ts", "sourceRoot": "", "sources": ["../../src/hooks/use-state.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAMH,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC;AAEpE,MAAM,WAAW,SAAS,CAAC,CAAC;IAC1B,KAAK,EAAE,CAAC,CAAC;IACT,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;CAC3B;AAED;;GAEG;AACH,wBAAgB,QAAQ,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CA2BvE;AAED;;GAEG;AACH,qBAAa,YAAY,CAAC,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IACrD,OAAO,CAAC,KAAK,CAAI;IACjB,OAAO,CAAC,SAAS,CAAiC;gBAEtC,YAAY,EAAE,CAAC;IAI3B,QAAQ,IAAI,CAAC;IAIb,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IAc/D,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,GAAG,MAAM,IAAI;IAKnD,OAAO,CAAC,eAAe;CAGxB;AAED;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,CAAC,EAChC,YAAY,EAAE,CAAC,EACf,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK,IAAI,GAC5B,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAW5B;AAED;;GAEG;AACH,wBAAgB,WAAW,CAAC,CAAC,EAAE,CAAC,SAAS,GAAG,EAAE,EAC5C,YAAY,EAAE,CAAC,EACf,SAAS,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,KAAK,CAAC,GAC3B,MAAM,CAAC,CAYT;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,CAAC,EACjC,GAAG,EAAE,MAAM,EACX,YAAY,EAAE,CAAC,EACf,OAAO,CAAC,EAAE;IACR,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,CAAC;IACxC,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;CAC/C,GACA,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CA2D5B;AAED;;GAEG;AACH,wBAAgB,iBAAiB,CAAC,CAAC,EACjC,YAAY,EAAE,CAAC,EACf,KAAK,GAAE,MAAY,GAClB,CAAC,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAmBrC"}