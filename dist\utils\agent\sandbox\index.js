/**
 * Multi-Platform Sandbox System
 *
 * Provides secure command execution with platform-specific isolation,
 * resource limits, and safety controls.
 */
import { platform } from 'os';
import { WindowsSandbox } from './windows-sandbox.js';
import { UnixSandbox } from './unix-sandbox.js';
import { logDebug, logError, logWarn, toError } from '../../logger/log.js';
export class BaseSandbox {
    options;
    constructor(options = {}) {
        this.options = {
            workingDirectory: options.workingDirectory || process.cwd(),
            timeout: options.timeout || 30000,
            maxMemory: options.maxMemory || 512 * 1024 * 1024, // 512MB
            maxCpuTime: options.maxCpuTime || 10000, // 10 seconds
            allowNetworking: options.allowNetworking ?? false,
            allowFileSystem: options.allowFileSystem ?? true,
            restrictedPaths: options.restrictedPaths || [],
            environmentVariables: options.environmentVariables || {},
            uid: options.uid || process.getuid?.() || 0,
            gid: options.gid || process.getgid?.() || 0,
        };
    }
    /**
     * Validate command before execution
     */
    validateCommand(input) {
        const errors = [];
        // Check command format
        if (!input.command || input.command.length === 0) {
            errors.push('Command cannot be empty');
        }
        // Check for dangerous commands
        const dangerousCommands = [
            'rm', 'rmdir', 'del', 'format', 'fdisk',
            'sudo', 'su', 'chmod', 'chown',
            'kill', 'killall', 'shutdown', 'reboot',
        ];
        const baseCommand = input.command[0]?.toLowerCase();
        if (baseCommand && dangerousCommands.includes(baseCommand)) {
            errors.push(`Potentially dangerous command: ${baseCommand}`);
        }
        // Check working directory
        if (input.workdir && this.options.restrictedPaths.length > 0) {
            const isRestricted = this.options.restrictedPaths.some(path => input.workdir?.startsWith(path));
            if (isRestricted) {
                errors.push(`Access to restricted path: ${input.workdir}`);
            }
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    /**
     * Apply resource limits to command
     */
    applyResourceLimits(command) {
        const platform = process.platform;
        if (platform === 'win32') {
            // Windows doesn't have built-in resource limiting like Unix
            return command;
        }
        // Unix-like systems can use ulimit or timeout
        const limitedCommand = ['timeout', `${this.options.timeout / 1000}s`];
        // Add memory limit if supported
        if (this.options.maxMemory > 0) {
            limitedCommand.push('--preserve-status');
        }
        limitedCommand.push(...command);
        return limitedCommand;
    }
    /**
     * Sanitize environment variables
     */
    sanitizeEnvironment() {
        const env = { ...process.env };
        // Remove potentially dangerous environment variables
        const dangerousVars = [
            'LD_PRELOAD', 'LD_LIBRARY_PATH', 'DYLD_INSERT_LIBRARIES',
            'PYTHONPATH', 'NODE_PATH', 'RUBYLIB',
        ];
        for (const varName of dangerousVars) {
            delete env[varName];
        }
        // Add custom environment variables
        Object.assign(env, this.options.environmentVariables);
        return env;
    }
    /**
     * Log sandbox activity
     */
    logActivity(action, details) {
        logDebug(`Sandbox ${action}:`, details);
    }
}
/**
 * Create platform-appropriate sandbox
 */
export function createSandbox(options = {}) {
    const currentPlatform = platform();
    switch (currentPlatform) {
        case 'win32':
            return new WindowsSandbox(options);
        case 'linux':
        case 'darwin':
        case 'freebsd':
        case 'openbsd':
            return new UnixSandbox(options);
        default:
            logWarn(`Unsupported platform for sandboxing: ${currentPlatform}`);
            return new UnixSandbox(options); // Fallback to Unix sandbox
    }
}
/**
 * Check if sandboxing is supported on current platform
 */
export async function isSandboxingSupported() {
    try {
        const sandbox = createSandbox();
        return await sandbox.isAvailable();
    }
    catch (error) {
        const err = toError(error);
        logError('Error checking sandbox support', err);
        return false;
    }
}
/**
 * Get sandbox capabilities for current platform
 */
export function getSandboxCapabilities() {
    const sandbox = createSandbox();
    return sandbox.getCapabilities();
}
/**
 * Execute command with automatic sandbox selection
 */
export async function executeInSandbox(input, options = {}) {
    const sandbox = createSandbox(options);
    try {
        await sandbox.setup();
        const result = await sandbox.execute(input);
        await sandbox.cleanup();
        return result;
    }
    catch (error) {
        await sandbox.cleanup();
        throw error;
    }
}
/**
 * Test sandbox functionality
 */
export async function testSandbox(options = {}) {
    const sandbox = createSandbox(options);
    const capabilities = sandbox.getCapabilities();
    const testResults = [];
    // Test basic execution
    try {
        await sandbox.setup();
        const basicTest = await sandbox.execute({
            command: ['echo', 'test'],
        });
        testResults.push({
            test: 'Basic command execution',
            passed: basicTest.exitCode === 0 && basicTest.stdout.includes('test'),
        });
    }
    catch (error) {
        const err = toError(error);
        testResults.push({
            test: 'Basic command execution',
            passed: false,
            error: err.message,
        });
    }
    // Test timeout handling
    try {
        const timeoutTest = await sandbox.execute({
            command: ['sleep', '2'],
        });
        testResults.push({
            test: 'Timeout handling',
            passed: timeoutTest.exitCode !== 0, // Should timeout
        });
    }
    catch (_error) {
        testResults.push({
            test: 'Timeout handling',
            passed: true, // Timeout is expected
        });
    }
    await sandbox.cleanup();
    return {
        supported: await sandbox.isAvailable(),
        capabilities,
        testResults,
    };
}
export default {
    createSandbox,
    executeInSandbox,
    isSandboxingSupported,
    getSandboxCapabilities,
    testSandbox,
    BaseSandbox,
};
//# sourceMappingURL=index.js.map