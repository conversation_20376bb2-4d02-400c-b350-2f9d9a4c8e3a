/**
 * Configuration Management System
 *
 * Handles multi-format configuration loading, environment variables,
 * and configuration hierarchy management.
 */
import type { AppConfig } from '../types/index.js';
/**
 * Load configuration from multiple sources with proper hierarchy
 */
export declare function loadConfig(): AppConfig;
/**
 * Discover project documentation path
 */
export declare function discoverProjectDocPath(startDir: string): string | null;
/**
 * Save configuration to file
 */
export declare function saveConfig(config: Partial<AppConfig>, global?: boolean): void;
/**
 * Get API key for a provider
 */
export declare function getApiKey(provider?: string): string | undefined;
/**
 * Get base URL for a provider
 */
export declare function getBaseUrl(provider?: string): string;
/**
 * Clear configuration cache
 */
export declare function clearConfigCache(): void;
/**
 * Get configuration file path
 */
export declare function getConfigPath(global?: boolean): string;
/**
 * Validate configuration
 */
export declare function validateConfig(config: AppConfig): {
    valid: boolean;
    errors: string[];
};
//# sourceMappingURL=config.d.ts.map