{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/utils/agent/sandbox/index.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AAC9B,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAC;AAyB3E,MAAM,OAAgB,WAAW;IACrB,OAAO,CAA2B;IAE5C,YAAY,UAA0B,EAAE;QACtC,IAAI,CAAC,OAAO,GAAG;YACb,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE;YAC3D,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,KAAK;YACjC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ;YAC3D,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,KAAK,EAAE,aAAa;YACtD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,KAAK;YACjD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,IAAI;YAChD,eAAe,EAAE,OAAO,CAAC,eAAe,IAAI,EAAE;YAC9C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,IAAI,EAAE;YACxD,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;YAC3C,GAAG,EAAE,OAAO,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;SAC5C,CAAC;IACJ,CAAC;IA2BD;;OAEG;IACO,eAAe,CAAC,KAAgB;QACxC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,uBAAuB;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;QACzC,CAAC;QAED,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG;YACxB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;YACvC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO;YAC9B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ;SACxC,CAAC;QAEF,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QACpD,IAAI,WAAW,IAAI,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,kCAAkC,WAAW,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,0BAA0B;QAC1B,IAAI,KAAK,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7D,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC5D,KAAK,CAAC,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,CAChC,CAAC;YAEF,IAAI,YAAY,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACO,mBAAmB,CAAC,OAAiB;QAC7C,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,4DAA4D;YAC5D,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,8CAA8C;QAC9C,MAAM,cAAc,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC;QAEtE,gCAAgC;QAChC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAC/B,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC3C,CAAC;QAED,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;QAChC,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACO,mBAAmB;QAC3B,MAAM,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAE/B,qDAAqD;QACrD,MAAM,aAAa,GAAG;YACpB,YAAY,EAAE,iBAAiB,EAAE,uBAAuB;YACxD,YAAY,EAAE,WAAW,EAAE,SAAS;SACrC,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE,CAAC;YACpC,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;QACtB,CAAC;QAED,mCAAmC;QACnC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC;QAEtD,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,MAAc,EAAE,OAAY;QAChD,QAAQ,CAAC,WAAW,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC;IAC1C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,UAA0B,EAAE;IACxD,MAAM,eAAe,GAAG,QAAQ,EAAE,CAAC;IAEnC,QAAQ,eAAe,EAAE,CAAC;QAC1B,KAAK,OAAO;YACV,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC;QACrC,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS,CAAC;QACf,KAAK,SAAS;YACZ,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC;YACE,OAAO,CAAC,wCAAwC,eAAe,EAAE,CAAC,CAAC;YACnE,OAAO,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,2BAA2B;IAC9D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IACzC,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,aAAa,EAAE,CAAC;QAChC,OAAO,MAAM,OAAO,CAAC,WAAW,EAAE,CAAC;IACrC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3B,QAAQ,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,sBAAsB;IACpC,MAAM,OAAO,GAAG,aAAa,EAAE,CAAC;IAChC,OAAO,OAAO,CAAC,eAAe,EAAE,CAAC;AACnC,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,KAAgB,EAChB,UAA0B,EAAE;IAE5B,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAEvC,IAAI,CAAC;QACH,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;QACtB,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5C,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;QACxB,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,UAA0B,EAAE;IAK5D,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IACvC,MAAM,YAAY,GAAG,OAAO,CAAC,eAAe,EAAE,CAAC;IAC/C,MAAM,WAAW,GAA6D,EAAE,CAAC;IAEjF,uBAAuB;IACvB,IAAI,CAAC;QACH,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;QAEtB,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC;YACtC,OAAO,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;SAC1B,CAAC,CAAC;QAEH,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;SACtE,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAC3B,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,yBAAyB;YAC/B,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,GAAG,CAAC,OAAO;SACnB,CAAC,CAAC;IACL,CAAC;IAED,wBAAwB;IACxB,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC;YACxC,OAAO,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC;SACxB,CAAC,CAAC;QAEH,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,WAAW,CAAC,QAAQ,KAAK,CAAC,EAAE,iBAAiB;SACtD,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,MAAM,EAAE,CAAC;QAChB,WAAW,CAAC,IAAI,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,MAAM,EAAE,IAAI,EAAE,sBAAsB;SACrC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;IAExB,OAAO;QACL,SAAS,EAAE,MAAM,OAAO,CAAC,WAAW,EAAE;QACtC,YAAY;QACZ,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,eAAe;IACb,aAAa;IACb,gBAAgB;IAChB,qBAAqB;IACrB,sBAAsB;IACtB,WAAW;IACX,WAAW;CACZ,CAAC"}