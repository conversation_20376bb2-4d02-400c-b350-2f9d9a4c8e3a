/**
 * Context Limit Management
 * 
 * Handles intelligent file selection and context optimization
 * to fit within model token limits while preserving important information.
 */


import { extname, basename } from 'path';
import { logInfo, logDebug } from '../logger/log.js';
import type { FileContent } from '../../types/index.js';

export interface SizeMap {
  totalFiles: number;
  totalSize: number;
  filesByType: Record<string, number>;
  sizeByType: Record<string, number>;
  largestFiles: Array<{ path: string; size: number }>;
}

export interface ContextLimits {
  maxTokens: number;
  maxFiles: number;
  maxFileSize: number;
  priorityExtensions: string[];
  excludeExtensions: string[];
}

/**
 * Compute size map for directory analysis
 */
export function computeSizeMap(files: FileContent[]): SizeMap {
  const sizeMap: SizeMap = {
    totalFiles: files.length,
    totalSize: 0,
    filesByType: {},
    sizeByType: {},
    largestFiles: [],
  };

  for (const file of files) {
    const ext = extname(file.path).toLowerCase() || 'no-extension';
    
    sizeMap.totalSize += file.size;
    sizeMap.filesByType[ext] = (sizeMap.filesByType[ext] || 0) + 1;
    sizeMap.sizeByType[ext] = (sizeMap.sizeByType[ext] || 0) + file.size;
    
    sizeMap.largestFiles.push({ path: file.path, size: file.size });
  }

  // Sort largest files
  sizeMap.largestFiles.sort((a, b) => b.size - a.size);
  sizeMap.largestFiles = sizeMap.largestFiles.slice(0, 20); // Top 20

  return sizeMap;
}

/**
 * Optimize file selection for context limits
 */
export function optimizeForContextLimits(
  files: FileContent[],
  limits: ContextLimits,
): FileContent[] {
  logInfo(`Optimizing ${files.length} files for context limits`);
  
  // Step 1: Filter by file size
  const sizeFiltered = files.filter(file => file.size <= limits.maxFileSize);
  logDebug(`After size filtering: ${sizeFiltered.length} files`);

  // Step 2: Filter by extension
  const extensionFiltered = sizeFiltered.filter(file => {
    const ext = extname(file.path).toLowerCase();
    
    if (limits.excludeExtensions.includes(ext)) {
      return false;
    }
    
    return true;
  });
  logDebug(`After extension filtering: ${extensionFiltered.length} files`);

  // Step 3: Prioritize files
  const prioritized = prioritizeFiles(extensionFiltered, limits.priorityExtensions);
  
  // Step 4: Select files within token limit
  const selected = selectFilesWithinTokenLimit(prioritized, limits.maxTokens);
  
  // Step 5: Ensure we don't exceed max files
  const final = selected.slice(0, limits.maxFiles);
  
  logInfo(`Final selection: ${final.length} files`);
  return final;
}

/**
 * Prioritize files based on importance
 */
function prioritizeFiles(files: FileContent[], priorityExtensions: string[]): FileContent[] {
  return files.sort((a, b) => {
    const scoreA = calculateFileImportance(a, priorityExtensions);
    const scoreB = calculateFileImportance(b, priorityExtensions);
    
    return scoreB - scoreA; // Higher score first
  });
}

/**
 * Calculate file importance score
 */
function calculateFileImportance(file: FileContent, priorityExtensions: string[]): number {
  let score = 0;
  const ext = extname(file.path).toLowerCase();
  const name = basename(file.path).toLowerCase();
  
  // Priority by extension
  const extIndex = priorityExtensions.indexOf(ext);
  if (extIndex !== -1) {
    score += (priorityExtensions.length - extIndex) * 10;
  }
  
  // Priority by filename
  if (name.includes('readme')) {score += 50;}
  if (name.includes('package.json')) {score += 45;}
  if (name.includes('tsconfig.json')) {score += 40;}
  if (name.includes('config')) {score += 30;}
  if (name.includes('index')) {score += 25;}
  if (name.includes('main')) {score += 20;}
  if (name.includes('app')) {score += 15;}
  if (name.includes('cli')) {score += 15;}
  if (name.includes('test') || name.includes('spec')) {score -= 10;} // Lower priority for tests
  
  // Priority by path depth (prefer root files)
  const depth = file.path.split('/').length;
  score += Math.max(0, 10 - depth);
  
  // Penalty for very large files
  if (file.size > 50 * 1024) {score -= 20;}
  if (file.size > 100 * 1024) {score -= 50;}
  
  return score;
}

/**
 * Select files within token limit
 */
function selectFilesWithinTokenLimit(files: FileContent[], maxTokens: number): FileContent[] {
  const selected: FileContent[] = [];
  let totalTokens = 0;
  
  for (const file of files) {
    const fileTokens = estimateTokens(file.content);
    
    if (totalTokens + fileTokens <= maxTokens) {
      selected.push(file);
      totalTokens += fileTokens;
    } else {
      // Try to include a truncated version
      const remainingTokens = maxTokens - totalTokens;
      if (remainingTokens > 100) { // Only if we have meaningful space left
        const truncatedContent = truncateContent(file.content, remainingTokens);
        selected.push({
          ...file,
          content: truncatedContent,
          size: truncatedContent.length,
        });
        break;
      }
    }
  }
  
  logDebug(`Selected files use approximately ${totalTokens} tokens`);
  return selected;
}

/**
 * Estimate token count for text
 */
function estimateTokens(text: string): number {
  // Rough estimation: ~4 characters per token for code
  // Add overhead for formatting and structure
  const baseTokens = Math.ceil(text.length / 4);
  const overhead = Math.ceil(baseTokens * 0.1); // 10% overhead
  
  return baseTokens + overhead;
}

/**
 * Truncate content to fit within token limit
 */
function truncateContent(content: string, maxTokens: number): string {
  const maxChars = maxTokens * 4; // Rough conversion
  
  if (content.length <= maxChars) {
    return content;
  }
  
  // Try to truncate at a reasonable boundary
  const lines = content.split('\n');
  let truncated = '';
  
  for (const line of lines) {
    if (truncated.length + line.length + 1 <= maxChars - 100) { // Leave buffer
      truncated += line + '\n';
    } else {
      break;
    }
  }
  
  truncated += '\n... [Content truncated to fit context limit] ...';
  
  return truncated;
}

/**
 * Get default context limits for a model
 */
export function getDefaultContextLimits(model: string): ContextLimits {
  const limits: ContextLimits = {
    maxTokens: 32000, // Conservative default
    maxFiles: 50,
    maxFileSize: 100 * 1024, // 100KB
    priorityExtensions: [
      '.ts', '.tsx', '.js', '.jsx',
      '.py', '.java', '.go', '.rs',
      '.md', '.json', '.yaml', '.yml',
      '.html', '.css', '.scss',
    ],
    excludeExtensions: [
      '.log', '.tmp', '.cache', '.lock',
      '.min.js', '.min.css',
      '.map', '.d.ts',
    ],
  };

  // Adjust based on model
  if (model.includes('gpt-4')) {
    limits.maxTokens = model.includes('turbo') ? 100000 : 6000;
  } else if (model.includes('gpt-3.5')) {
    limits.maxTokens = 3000;
  } else if (model.includes('gemini-1.5')) {
    limits.maxTokens = 800000; // Very large context
    limits.maxFiles = 200;
  } else if (model.includes('claude-3')) {
    limits.maxTokens = 150000;
    limits.maxFiles = 100;
  }

  return limits;
}

/**
 * Analyze context usage
 */
export function analyzeContextUsage(files: FileContent[]): {
  totalTokens: number;
  tokensByType: Record<string, number>;
  efficiency: number;
  recommendations: string[];
} {
  let totalTokens = 0;
  const tokensByType: Record<string, number> = {};
  const recommendations: string[] = [];

  for (const file of files) {
    const ext = extname(file.path).toLowerCase() || 'no-extension';
    const tokens = estimateTokens(file.content);
    
    totalTokens += tokens;
    tokensByType[ext] = (tokensByType[ext] || 0) + tokens;
  }

  // Calculate efficiency (useful content vs overhead)
  const codeExtensions = ['.ts', '.tsx', '.js', '.jsx', '.py', '.java', '.go', '.rs'];
  const codeTokens = codeExtensions.reduce((sum, ext) => sum + (tokensByType[ext] || 0), 0);
  const efficiency = totalTokens > 0 ? (codeTokens / totalTokens) * 100 : 0;

  // Generate recommendations
  if (efficiency < 50) {
    recommendations.push('Consider excluding documentation and config files to focus on code');
  }
  
  if (tokensByType['.md'] && tokensByType['.md'] > totalTokens * 0.3) {
    recommendations.push('Markdown files are using significant context - consider summarizing');
  }
  
  if (files.length > 100) {
    recommendations.push('Large number of files - consider focusing on specific modules');
  }

  return {
    totalTokens,
    tokensByType,
    efficiency,
    recommendations,
  };
}

/**
 * Smart file chunking for very large files
 */
export function chunkLargeFile(file: FileContent, maxChunkTokens: number): FileContent[] {
  const tokens = estimateTokens(file.content);
  
  if (tokens <= maxChunkTokens) {
    return [file];
  }

  const chunks: FileContent[] = [];
  const lines = file.content.split('\n');
  let currentChunk = '';
  let chunkIndex = 0;

  for (const line of lines) {
    const lineTokens = estimateTokens(line);
    const chunkTokens = estimateTokens(currentChunk);

    if (chunkTokens + lineTokens > maxChunkTokens && currentChunk) {
      // Save current chunk
      chunks.push({
        path: `${file.path}#chunk${chunkIndex}`,
        content: currentChunk,
        size: currentChunk.length,
        mimeType: file.mimeType,
      });

      currentChunk = line + '\n';
      chunkIndex++;
    } else {
      currentChunk += line + '\n';
    }
  }

  // Add final chunk
  if (currentChunk) {
    chunks.push({
      path: `${file.path}#chunk${chunkIndex}`,
      content: currentChunk,
      size: currentChunk.length,
      mimeType: file.mimeType,
    });
  }

  return chunks;
}
