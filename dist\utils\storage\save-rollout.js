/**
 * Session Rollout System
 *
 * Handles automatic session persistence with best-effort async saving
 * without blocking the UI. Provides session recovery and management.
 */
import { writeFileSync, readFileSync, existsSync, mkdirSync, readdirSync, unlinkSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { format } from 'date-fns';
import { logInfo, logError, logWarn } from '../logger/log.js';
import { toError } from '../error-utils.js';
// Session storage configuration
const SESSIONS_DIR = join(homedir(), '.kritrima-ai', 'sessions');
const MAX_SESSIONS = 100; // Maximum number of sessions to keep
/**
 * Save session rollout with best-effort async processing
 */
export function saveRollout(sessionId, items) {
    // Best-effort async saving without blocking UI
    saveRolloutAsync(sessionId, items).catch((error) => {
        logError('Failed to save session rollout', error);
    });
}
/**
 * Async session saving implementation
 */
async function saveRolloutAsync(sessionId, items) {
    try {
        // Ensure sessions directory exists
        ensureSessionsDirectory();
        // Create session data
        const sessionData = {
            id: sessionId,
            timestamp: Date.now(),
            items,
            config: {
                // Store minimal config for session recovery
                model: process.env.KRITRIMA_AI_MODEL || 'gpt-4',
                provider: process.env.KRITRIMA_AI_PROVIDER || 'openai',
                approvalMode: process.env.KRITRIMA_AI_APPROVAL_MODE || 'suggest',
            },
            metadata: {
                model: process.env.KRITRIMA_AI_MODEL || 'gpt-4',
                provider: process.env.KRITRIMA_AI_PROVIDER || 'openai',
                duration: calculateSessionDuration(items),
                messageCount: items.length,
                tokenUsage: calculateTokenUsage(items),
            },
        };
        // Generate filename with timestamp
        const timestamp = format(new Date(), 'yyyy-MM-dd-HH-mm-ss');
        const filename = `rollout-${timestamp}-${sessionId}.json`;
        const filePath = join(SESSIONS_DIR, filename);
        // Save session data
        writeFileSync(filePath, JSON.stringify(sessionData, null, 2));
        logInfo(`Session saved: ${filename}`);
        // Cleanup old sessions
        await cleanupOldSessions();
    }
    catch (error) {
        logError('Failed to save session rollout', toError(error));
        throw error;
    }
}
/**
 * Ensure sessions directory exists
 */
function ensureSessionsDirectory() {
    if (!existsSync(SESSIONS_DIR)) {
        mkdirSync(SESSIONS_DIR, { recursive: true });
    }
}
/**
 * Calculate session duration from items
 */
function calculateSessionDuration(items) {
    if (items.length === 0) {
        return 0;
    }
    const firstTimestamp = items[0].timestamp;
    const lastTimestamp = items[items.length - 1].timestamp;
    return lastTimestamp - firstTimestamp;
}
/**
 * Calculate token usage from items
 */
function calculateTokenUsage(items) {
    let prompt = 0;
    let completion = 0;
    for (const item of items) {
        // Rough estimation based on content length
        for (const content of item.content) {
            if (content.type === 'input_text' && content.text) {
                prompt += Math.ceil(content.text.length / 4);
            }
            else if (content.type === 'output_text' && content.text) {
                completion += Math.ceil(content.text.length / 4);
            }
        }
    }
    return {
        prompt,
        completion,
        total: prompt + completion,
    };
}
/**
 * Load session by ID
 */
export function loadSession(sessionId) {
    try {
        const files = readdirSync(SESSIONS_DIR);
        const sessionFile = files.find(file => file.includes(sessionId));
        if (!sessionFile) {
            return null;
        }
        const filePath = join(SESSIONS_DIR, sessionFile);
        const content = readFileSync(filePath, 'utf-8');
        return JSON.parse(content);
    }
    catch (error) {
        logError(`Failed to load session ${sessionId}`, toError(error));
        return null;
    }
}
/**
 * List all available sessions
 */
export function listSessions() {
    try {
        ensureSessionsDirectory();
        const files = readdirSync(SESSIONS_DIR)
            .filter(file => file.startsWith('rollout-') && file.endsWith('.json'))
            .sort()
            .reverse(); // Most recent first
        const sessions = [];
        for (const file of files) {
            try {
                const filePath = join(SESSIONS_DIR, file);
                const content = readFileSync(filePath, 'utf-8');
                const session = JSON.parse(content);
                sessions.push(session);
            }
            catch (error) {
                logWarn(`Failed to parse session file: ${file}`, error);
            }
        }
        return sessions;
    }
    catch (error) {
        logError('Failed to list sessions', toError(error));
        return [];
    }
}
/**
 * Delete session by ID
 */
export function deleteSession(sessionId) {
    try {
        const files = readdirSync(SESSIONS_DIR);
        const sessionFile = files.find(file => file.includes(sessionId));
        if (!sessionFile) {
            return false;
        }
        const filePath = join(SESSIONS_DIR, sessionFile);
        unlinkSync(filePath);
        logInfo(`Session deleted: ${sessionId}`);
        return true;
    }
    catch (error) {
        logError(`Failed to delete session ${sessionId}`, toError(error));
        return false;
    }
}
/**
 * Get session statistics
 */
export function getSessionStats() {
    const sessions = listSessions();
    if (sessions.length === 0) {
        return {
            totalSessions: 0,
            totalMessages: 0,
            totalDuration: 0,
            averageSessionLength: 0,
        };
    }
    const totalMessages = sessions.reduce((sum, session) => sum + session.metadata.messageCount, 0);
    const totalDuration = sessions.reduce((sum, session) => sum + session.metadata.duration, 0);
    const averageSessionLength = totalMessages / sessions.length;
    const timestamps = sessions.map(s => s.timestamp).sort();
    const oldestSession = new Date(timestamps[0]);
    const newestSession = new Date(timestamps[timestamps.length - 1]);
    return {
        totalSessions: sessions.length,
        totalMessages,
        totalDuration,
        averageSessionLength,
        oldestSession,
        newestSession,
    };
}
/**
 * Cleanup old sessions to maintain storage limits
 */
async function cleanupOldSessions() {
    try {
        const sessions = listSessions();
        if (sessions.length <= MAX_SESSIONS) {
            return;
        }
        // Sort by timestamp (oldest first)
        const sortedSessions = sessions.sort((a, b) => a.timestamp - b.timestamp);
        const sessionsToDelete = sortedSessions.slice(0, sessions.length - MAX_SESSIONS);
        for (const session of sessionsToDelete) {
            deleteSession(session.id);
        }
        logInfo(`Cleaned up ${sessionsToDelete.length} old sessions`);
    }
    catch (error) {
        logError('Failed to cleanup old sessions', toError(error));
    }
}
/**
 * Export session to file
 */
export function exportSession(sessionId, outputPath) {
    try {
        const session = loadSession(sessionId);
        if (!session) {
            return false;
        }
        writeFileSync(outputPath, JSON.stringify(session, null, 2));
        logInfo(`Session exported to: ${outputPath}`);
        return true;
    }
    catch (error) {
        logError(`Failed to export session ${sessionId}`, toError(error));
        return false;
    }
}
/**
 * Import session from file
 */
export function importSession(inputPath) {
    try {
        const content = readFileSync(inputPath, 'utf-8');
        const session = JSON.parse(content);
        // Validate session data
        if (!session.id || !session.items || !Array.isArray(session.items)) {
            throw new Error('Invalid session data format');
        }
        // Save as new session
        saveRollout(session.id, session.items);
        logInfo(`Session imported: ${session.id}`);
        return session.id;
    }
    catch (error) {
        logError(`Failed to import session from ${inputPath}`, toError(error));
        return null;
    }
}
/**
 * Search sessions by content
 */
export function searchSessions(query) {
    const sessions = listSessions();
    const lowerQuery = query.toLowerCase();
    return sessions.filter(session => {
        // Search in session items
        for (const item of session.items) {
            for (const content of item.content) {
                if (content.type === 'input_text' && content.text) {
                    if (content.text.toLowerCase().includes(lowerQuery)) {
                        return true;
                    }
                }
            }
        }
        return false;
    });
}
//# sourceMappingURL=save-rollout.js.map