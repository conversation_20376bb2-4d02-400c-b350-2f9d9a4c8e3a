{"version": 3, "file": "use-state.js", "sourceRoot": "", "sources": ["../../src/hooks/use-state.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AACxE,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAC5B,OAAO,EAAE,OAAO,EAAE,MAAM,IAAI,CAAC;AAS7B;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAI,YAAe;IACzC,IAAI,YAAY,GAAG,YAAY,CAAC;IAChC,MAAM,SAAS,GAAG,IAAI,GAAG,EAAc,CAAC;IAExC,MAAM,QAAQ,GAAG,GAAM,EAAE,CAAC,YAAY,CAAC;IAEvC,MAAM,QAAQ,GAAoB,CAAC,KAAK,EAAE,EAAE;QAC1C,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,UAAU;YAC1C,CAAC,CAAE,KAAwB,CAAC,YAAY,CAAC;YACzC,CAAC,CAAC,KAAK,CAAC;QAEV,IAAI,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC9B,YAAY,GAAG,QAAQ,CAAC;YACxB,uBAAuB;YACvB,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,CAAC,QAAoB,EAAgB,EAAE;QACvD,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF,gEAAgE;IAC/D,QAAgB,CAAC,SAAS,GAAG,SAAS,CAAC;IAExC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,OAAO,YAAY;IACf,KAAK,CAAI;IACT,SAAS,GAAG,IAAI,GAAG,EAAsB,CAAC;IAElD,YAAY,YAAe;QACzB,IAAI,CAAC,KAAK,GAAG,EAAE,GAAG,YAAY,EAAE,CAAC;IACnC,CAAC;IAED,QAAQ;QACN,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED,QAAQ,CAAC,OAA+C;QACtD,MAAM,UAAU,GAAG,OAAO,OAAO,KAAK,UAAU;YAC9C,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;YACrB,CAAC,CAAC,OAAO,CAAC;QAEZ,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,UAAU,EAAE,CAAC;QAElD,kCAAkC;QAClC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5D,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,eAAe,EAAE,CAAC;QACzB,CAAC;IACH,CAAC;IAED,SAAS,CAAC,QAA4B;QACpC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7B,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEO,eAAe;QACrB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC3D,CAAC;CACF;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAC9B,YAAe,EACf,QAA6B;IAE7B,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;IAEpD,MAAM,gBAAgB,GAAoB,CAAC,KAAK,EAAE,EAAE;QAClD,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChB,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACtC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CACzB,YAAe,EACf,SAA4B;IAE5B,IAAI,WAAc,CAAC;IACnB,IAAI,QAAW,CAAC;IAEhB,OAAO,GAAG,EAAE;QACV,gCAAgC;QAChC,IAAI,CAAC,QAAQ,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACpE,WAAW,GAAG,SAAS,CAAC,GAAG,YAAY,CAAC,CAAC;YACzC,QAAQ,GAAG,CAAC,GAAG,YAAY,CAAM,CAAC;QACpC,CAAC;QACD,OAAO,WAAW,CAAC;IACrB,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAC/B,GAAW,EACX,YAAe,EACf,OAGC;IAED,MAAM,cAAc,GAAG;QACrB,OAAO,EAAE,CAAC,GAAW,EAAE,EAAE;YACvB,IAAI,CAAC;gBACH,oCAAoC;gBACpC,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;gBAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC;gBAEjD,IAAI,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACzB,OAAO,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACxC,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,EAAE,CAAC,GAAW,EAAE,KAAa,EAAE,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;gBAE5D,0BAA0B;gBAC1B,SAAS,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAE3C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC;gBACjD,aAAa,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACzC,CAAC;YAAC,MAAM,CAAC;gBACP,wBAAwB;YAC1B,CAAC;QACH,CAAC;KACF,CAAC;IAEF,MAAM,KAAK,GAAG,OAAO,IAAI,cAAc,CAAC;IAExC,kCAAkC;IAClC,IAAI,WAAc,CAAC;IACnB,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAClC,WAAW,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;IAC3D,CAAC;IAAC,MAAM,CAAC;QACP,WAAW,GAAG,YAAY,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,QAAQ,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IAE3D,MAAM,QAAQ,GAAoB,CAAC,KAAK,EAAE,EAAE;QAC1C,gBAAgB,CAAC,KAAK,CAAC,CAAC;QAExB,qBAAqB;QACrB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,OAAO,KAAK,KAAK,UAAU;gBAC1C,CAAC,CAAE,KAAwB,CAAC,QAAQ,EAAE,CAAC;gBACvC,CAAC,CAAC,KAAK,CAAC;YACV,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/C,CAAC;QAAC,MAAM,CAAC;YACP,wBAAwB;QAC1B,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAC/B,YAAe,EACf,QAAgB,GAAG;IAEnB,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;IACpD,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;IAEtE,IAAI,SAAS,GAA0B,IAAI,CAAC;IAE5C,MAAM,oBAAoB,GAAoB,CAAC,KAAK,EAAE,EAAE;QACtD,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEhB,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QAED,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YAC1B,iBAAiB,CAAC,QAAQ,EAAE,CAAC,CAAC;QAChC,CAAC,EAAE,KAAK,CAAC,CAAC;IACZ,CAAC,CAAC;IAEF,OAAO,CAAC,QAAQ,EAAE,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;AAC7D,CAAC"}