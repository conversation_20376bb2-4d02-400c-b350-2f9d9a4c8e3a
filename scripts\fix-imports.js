#!/usr/bin/env node

/**
 * Fix ES Module Imports
 * 
 * This script fixes the import statements in the compiled JavaScript files
 * to include the .js extension, which is required for ES modules.
 */

import { readdir, readFile, writeFile, stat } from 'fs/promises';
import { join, dirname, extname, relative } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const distDir = join(__dirname, '..', 'dist');

/**
 * Fix import statements in a JavaScript file
 */
async function fixImportsInFile(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    
    // Regular expression to match import statements
    const importRegex = /from\s+['"](\.[^'"]*)['"]/g;
    const dynamicImportRegex = /import\s*\(\s*['"](\.[^'"]*)['"]\s*\)/g;
    
    let modified = false;
    let newContent = content;
    
    // Fix static imports
    newContent = newContent.replace(importRegex, (match, importPath) => {
      if (!importPath.endsWith('.js') && !importPath.includes('?')) {
        modified = true;
        return match.replace(importPath, importPath + '.js');
      }
      return match;
    });
    
    // Fix dynamic imports
    newContent = newContent.replace(dynamicImportRegex, (match, importPath) => {
      if (!importPath.endsWith('.js') && !importPath.includes('?')) {
        modified = true;
        return match.replace(importPath, importPath + '.js');
      }
      return match;
    });
    
    if (modified) {
      await writeFile(filePath, newContent, 'utf-8');
      console.log(`Fixed imports in: ${relative(distDir, filePath)}`);
    }
  } catch (error) {
    console.error(`Error fixing imports in ${filePath}:`, error.message);
  }
}

/**
 * Recursively process all JavaScript files in a directory
 */
async function processDirectory(dirPath) {
  try {
    const entries = await readdir(dirPath);
    
    for (const entry of entries) {
      const fullPath = join(dirPath, entry);
      const stats = await stat(fullPath);
      
      if (stats.isDirectory()) {
        await processDirectory(fullPath);
      } else if (stats.isFile() && extname(entry) === '.js') {
        await fixImportsInFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error processing directory ${dirPath}:`, error.message);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('Fixing ES module imports in dist directory...');
  
  try {
    await processDirectory(distDir);
    console.log('✅ Import fixing completed successfully!');
  } catch (error) {
    console.error('❌ Error fixing imports:', error.message);
    process.exit(1);
  }
}

// Run the script
main();
