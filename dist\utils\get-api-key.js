/**
 * API Key Validation and Setup UI
 *
 * Interactive terminal interface for setting up and validating API keys
 * for different AI providers with secure input and validation.
 */
import blessed from 'blessed';
import { getApiKey } from './config.js';
import { getProvider, getProviderNames } from './providers.js';
import { createOpenAIClient } from './openai-client.js';
import { logDebug, logError } from './logger/log.js';
import { toError, getErrorMessage } from './error-utils.js';
/**
 * Interactive API key setup interface
 */
export class ApiKeySetup {
    screen;
    container;
    providerList;
    keyInput;
    statusBox;
    instructionsBox;
    providers = [];
    selectedProvider = '';
    onComplete;
    listFocused = true;
    selectedIndex = 0;
    constructor() {
        this.providers = getProviderNames();
        this.createScreen();
        this.createComponents();
        this.setupEventHandlers();
    }
    /**
     * Create blessed screen
     */
    createScreen() {
        this.screen = blessed.screen({
            smartCSR: true,
            title: 'Kritrima AI - API Key Setup',
        });
    }
    /**
     * Create UI components
     */
    createComponents() {
        // Main container
        this.container = blessed.box({
            parent: this.screen,
            top: 'center',
            left: 'center',
            width: '80%',
            height: '70%',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'cyan',
                },
            },
            label: ' API Key Setup ',
            tags: true,
        });
        // Instructions
        this.instructionsBox = blessed.box({
            parent: this.container,
            top: 0,
            left: 0,
            width: '100%',
            height: 6,
            content: this.getInstructions(),
            tags: true,
            style: {
                fg: 'white',
            },
        });
        // Provider list
        this.providerList = blessed.list({
            parent: this.container,
            top: 6,
            left: 0,
            width: '40%',
            height: '100%-12',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'yellow',
                },
                selected: {
                    bg: 'blue',
                },
            },
            label: ' Select Provider ',
            keys: true,
            vi: true,
            scrollable: true,
            items: this.providers.map(p => {
                const provider = getProvider(p);
                const hasKey = !!getApiKey(p);
                const status = hasKey ? '✓' : '✗';
                return `${status} ${provider?.name || p}`;
            }),
        });
        // API key input
        this.keyInput = blessed.textbox({
            parent: this.container,
            top: 6,
            left: '40%',
            width: '60%',
            height: 3,
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'green',
                },
            },
            label: ' API Key ',
            inputOnFocus: true,
            keys: true,
            hidden: true, // Hide input for security
        });
        // Status box
        this.statusBox = blessed.box({
            parent: this.container,
            top: 9,
            left: '40%',
            width: '60%',
            height: '100%-15',
            border: {
                type: 'line',
            },
            style: {
                border: {
                    fg: 'gray',
                },
            },
            label: ' Status ',
            scrollable: true,
            tags: true,
        });
        // Bottom instructions
        const _bottomBox = blessed.box({
            parent: this.container,
            bottom: 0,
            left: 0,
            width: '100%',
            height: 3,
            content: 'Tab: Switch | Enter: Set Key | Esc: Cancel | Ctrl+T: Test Key',
            style: {
                fg: 'gray',
            },
            tags: true,
        });
    }
    /**
     * Setup event handlers
     */
    setupEventHandlers() {
        // Global handlers
        this.screen.key(['escape', 'C-c'], () => {
            this.complete({ success: false, provider: '', hasValidKey: false });
        });
        this.screen.key(['tab'], () => {
            if (this.providerList.focused) {
                this.keyInput.focus();
            }
            else {
                this.providerList.focus();
            }
            this.screen.render();
        });
        this.screen.key(['C-t'], () => {
            this.testApiKey();
        });
        // Provider list handlers
        this.providerList.on('select', () => {
            this.onProviderSelect();
        });
        this.providerList.key(['j', 'down'], () => {
            this.providerList.down(1);
            this.updateProviderInfo();
            this.screen.render();
        });
        this.providerList.key(['k', 'up'], () => {
            this.providerList.up(1);
            this.updateProviderInfo();
            this.screen.render();
        });
        // API key input handlers
        this.keyInput.on('submit', (value) => {
            this.setApiKey(value);
        });
        this.keyInput.on('cancel', () => {
            this.providerList.focus();
        });
        // Initial focus
        this.providerList.focus();
        this.updateProviderInfo();
    }
    /**
     * Get setup instructions
     */
    getInstructions() {
        return [
            '{bold}{cyan-fg}API Key Setup{/cyan-fg}{/bold}',
            '',
            'Select a provider and enter your API key to get started.',
            'API keys are stored securely in environment variables.',
            'You can get API keys from the provider\'s website.',
        ].join('\n');
    }
    /**
     * Handle provider selection
     */
    onProviderSelect() {
        const selected = this.providerList.selected;
        if (selected >= 0 && selected < this.providers.length) {
            this.selectedProvider = this.providers[selected];
            this.keyInput.focus();
            this.updateProviderInfo();
        }
    }
    /**
     * Update provider information display
     */
    updateProviderInfo() {
        const selected = this.providerList.selected;
        if (selected >= 0 && selected < this.providers.length) {
            const providerName = this.providers[selected];
            const provider = getProvider(providerName);
            const currentKey = getApiKey(providerName);
            if (provider) {
                const content = [
                    `{bold}Provider:{/bold} ${provider.name}`,
                    `{bold}Environment Variable:{/bold} ${provider.envKey}`,
                    `{bold}Base URL:{/bold} ${provider.baseURL}`,
                    `{bold}Current Status:{/bold} ${currentKey ? '{green-fg}✓ Key Set{/green-fg}' : '{red-fg}✗ No Key{/red-fg}'}`,
                    '',
                    '{bold}Instructions:{/bold}',
                    this.getProviderInstructions(providerName),
                ];
                this.statusBox.setContent(content.join('\n'));
                this.keyInput.setLabel(` API Key for ${provider.name} `);
            }
        }
        this.screen.render();
    }
    /**
     * Get provider-specific instructions
     */
    getProviderInstructions(provider) {
        switch (provider.toLowerCase()) {
            case 'openai':
                return 'Get your API key from: https://platform.openai.com/api-keys';
            case 'anthropic':
                return 'Get your API key from: https://console.anthropic.com/';
            case 'gemini':
                return 'Get your API key from: https://makersuite.google.com/app/apikey';
            case 'mistral':
                return 'Get your API key from: https://console.mistral.ai/';
            case 'ollama':
                return 'Ollama runs locally. No API key required.';
            default:
                return 'Check the provider\'s documentation for API key setup.';
        }
    }
    /**
     * Set API key for selected provider
     */
    async setApiKey(apiKey) {
        if (!this.selectedProvider || !apiKey.trim()) {
            this.updateStatus('Please select a provider and enter an API key.');
            return;
        }
        try {
            // Set environment variable
            process.env[getProvider(this.selectedProvider)?.envKey || `${this.selectedProvider.toUpperCase()}_API_KEY`] = apiKey;
            this.updateStatus('Testing API key...');
            // Test the API key
            const isValid = await this.validateApiKey(this.selectedProvider, apiKey);
            if (isValid) {
                this.updateStatus('{green-fg}✓ API key is valid and working!{/green-fg}');
                this.updateProviderList();
                // Complete setup
                setTimeout(() => {
                    this.complete({
                        success: true,
                        provider: this.selectedProvider,
                        hasValidKey: true,
                    });
                }, 1500);
            }
            else {
                this.updateStatus('{red-fg}✗ API key validation failed. Please check your key.{/red-fg}');
            }
        }
        catch (error) {
            logError('Error setting API key', toError(error));
            this.updateStatus(`{red-fg}Error: ${getErrorMessage(error)}{/red-fg}`);
        }
    }
    /**
     * Test current API key
     */
    async testApiKey() {
        if (!this.selectedProvider) {
            this.updateStatus('Please select a provider first.');
            return;
        }
        const apiKey = getApiKey(this.selectedProvider);
        if (!apiKey) {
            this.updateStatus('No API key set for this provider.');
            return;
        }
        this.updateStatus('Testing API key...');
        try {
            const isValid = await this.validateApiKey(this.selectedProvider, apiKey);
            if (isValid) {
                this.updateStatus('{green-fg}✓ API key is valid and working!{/green-fg}');
            }
            else {
                this.updateStatus('{red-fg}✗ API key validation failed.{/red-fg}');
            }
        }
        catch (error) {
            this.updateStatus(`{red-fg}Error testing API key: ${getErrorMessage(error)}{/red-fg}`);
        }
    }
    /**
     * Validate API key by making a test request
     */
    async validateApiKey(provider, apiKey) {
        try {
            const client = createOpenAIClient({ provider, apiKey });
            // Make a simple test request
            if (provider.toLowerCase() === 'ollama') {
                // For Ollama, just check if we can list models
                await client.models.list();
            }
            else {
                // For other providers, make a minimal completion request
                await client.chat.completions.create({
                    model: getProvider(provider)?.defaultModel || 'gpt-3.5-turbo',
                    messages: [{ role: 'user', content: 'test' }],
                    max_tokens: 1,
                });
            }
            return true;
        }
        catch (error) {
            logDebug(`API key validation failed for ${provider}`, error);
            return false;
        }
    }
    /**
     * Update status display
     */
    updateStatus(message) {
        const currentContent = this.statusBox.getContent();
        const lines = currentContent.split('\n');
        // Keep provider info, add status
        const statusIndex = lines.findIndex(line => line.includes('Status:'));
        if (statusIndex >= 0) {
            lines.splice(statusIndex + 2);
        }
        lines.push('', `{bold}Status:{/bold} ${message}`);
        this.statusBox.setContent(lines.join('\n'));
        this.screen.render();
    }
    /**
     * Update provider list with current status
     */
    updateProviderList() {
        const items = this.providers.map(p => {
            const provider = getProvider(p);
            const hasKey = !!getApiKey(p);
            const status = hasKey ? '✓' : '✗';
            return `${status} ${provider?.name || p}`;
        });
        this.providerList.setItems(items);
        this.screen.render();
    }
    /**
     * Complete the setup process
     */
    complete(result) {
        this.screen.destroy();
        if (this.onComplete) {
            this.onComplete(result);
        }
    }
    /**
     * Show the setup interface
     */
    show() {
        return new Promise((resolve) => {
            this.onComplete = resolve;
            this.screen.render();
        });
    }
}
/**
 * Quick API key validation without UI
 */
export async function validateApiKey(provider, apiKey) {
    const key = apiKey || getApiKey(provider);
    if (!key) {
        return false;
    }
    try {
        const client = createOpenAIClient({ provider, apiKey: key });
        if (provider.toLowerCase() === 'ollama') {
            await client.models.list();
        }
        else {
            await client.chat.completions.create({
                model: getProvider(provider)?.defaultModel || 'gpt-3.5-turbo',
                messages: [{ role: 'user', content: 'test' }],
                max_tokens: 1,
            });
        }
        return true;
    }
    catch (_error) {
        return false;
    }
}
/**
 * Setup API key with interactive interface
 */
export async function setupApiKey(_options = {}) {
    const setup = new ApiKeySetup();
    return setup.show();
}
export default ApiKeySetup;
//# sourceMappingURL=get-api-key.js.map