/**
 * API Key Validation and Setup UI
 *
 * Interactive terminal interface for setting up and validating API keys
 * for different AI providers with secure input and validation.
 */
export interface ApiKeySetupOptions {
    provider?: string;
    required?: boolean;
    interactive?: boolean;
}
export interface ApiKeySetupResult {
    success: boolean;
    provider: string;
    hasValidKey: boolean;
    error?: string;
}
/**
 * Interactive API key setup interface
 */
export declare class ApiKeySetup {
    private screen;
    private container;
    private providerList;
    private keyInput;
    private statusBox;
    private instructionsBox;
    private providers;
    private selectedProvider;
    private onComplete?;
    private listFocused;
    private selectedIndex;
    constructor();
    /**
     * Create blessed screen
     */
    private createScreen;
    /**
     * Create UI components
     */
    private createComponents;
    /**
     * Setup event handlers
     */
    private setupEventHandlers;
    /**
     * Get setup instructions
     */
    private getInstructions;
    /**
     * Handle provider selection
     */
    private onProviderSelect;
    /**
     * Update provider information display
     */
    private updateProviderInfo;
    /**
     * Get provider-specific instructions
     */
    private getProviderInstructions;
    /**
     * Set API key for selected provider
     */
    private setApiKey;
    /**
     * Test current API key
     */
    private testApiKey;
    /**
     * Validate API key by making a test request
     */
    private validateApiKey;
    /**
     * Update status display
     */
    private updateStatus;
    /**
     * Update provider list with current status
     */
    private updateProviderList;
    /**
     * Complete the setup process
     */
    private complete;
    /**
     * Show the setup interface
     */
    show(): Promise<ApiKeySetupResult>;
}
/**
 * Quick API key validation without UI
 */
export declare function validateApiKey(provider: string, apiKey?: string): Promise<boolean>;
/**
 * Setup API key with interactive interface
 */
export declare function setupApiKey(_options?: ApiKeySetupOptions): Promise<ApiKeySetupResult>;
export default ApiKeySetup;
//# sourceMappingURL=get-api-key.d.ts.map