/**
 * Test Setup Configuration
 * 
 * Global test setup for Vitest test runner.
 * This file is executed before all test files.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { resolve } from 'path';
import { existsSync, mkdirSync, rmSync } from 'fs';

// Test environment setup
beforeAll(() => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.VITEST = 'true';
  
  // Disable logging during tests
  process.env.KRITRIMA_DEBUG = 'false';
  process.env.KRITRIMA_NOTIFICATIONS = 'false';
  
  // Create test directories if they don't exist
  const testDirs = [
    resolve(process.cwd(), 'tests', 'fixtures'),
    resolve(process.cwd(), 'tests', 'temp'),
  ];
  
  testDirs.forEach(dir => {
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
  });
});

// Cleanup after all tests
afterAll(() => {
  // Clean up test directories
  const tempDir = resolve(process.cwd(), 'tests', 'temp');
  if (existsSync(tempDir)) {
    rmSync(tempDir, { recursive: true, force: true });
  }
});

// Setup before each test
beforeEach(() => {
  // Reset environment variables that might be modified by tests
  delete process.env.OPENAI_API_KEY;
  delete process.env.ANTHROPIC_API_KEY;
  delete process.env.KRITRIMA_MODEL;
  delete process.env.KRITRIMA_PROVIDER;
});

// Cleanup after each test
afterEach(() => {
  // Reset any global state that might have been modified
  // This ensures test isolation
});

// Mock console methods to reduce noise during testing
const originalConsole = { ...console };

// Optionally suppress console output during tests
if (process.env.SUPPRESS_TEST_LOGS === 'true') {
  console.log = () => {};
  console.info = () => {};
  console.warn = () => {};
  console.error = () => {};
}

// Restore console after tests if needed
afterAll(() => {
  if (process.env.SUPPRESS_TEST_LOGS === 'true') {
    Object.assign(console, originalConsole);
  }
});

// Global test utilities
declare global {
  var testUtils: {
    createTempDir: () => string;
    cleanupTempDir: (dir: string) => void;
  };
}

// Add global test utilities
globalThis.testUtils = {
  createTempDir: () => {
    const tempDir = resolve(process.cwd(), 'tests', 'temp', `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
    mkdirSync(tempDir, { recursive: true });
    return tempDir;
  },
  
  cleanupTempDir: (dir: string) => {
    if (existsSync(dir)) {
      rmSync(dir, { recursive: true, force: true });
    }
  }
};
