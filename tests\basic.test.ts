/**
 * Basic Tests
 * 
 * Basic functionality tests for the Kritrima AI CLI.
 */

import { describe, it, expect } from 'vitest';
import { existsSync } from 'fs';
import { resolve } from 'path';

describe('Project Structure', () => {
  it('should have required package.json', () => {
    const packagePath = resolve(process.cwd(), 'package.json');
    expect(existsSync(packagePath)).toBe(true);
  });

  it('should have TypeScript configuration', () => {
    const tsconfigPath = resolve(process.cwd(), 'tsconfig.json');
    expect(existsSync(tsconfigPath)).toBe(true);
  });

  it('should have main CLI entry point', () => {
    const cliPath = resolve(process.cwd(), 'src', 'cli.tsx');
    expect(existsSync(cliPath)).toBe(true);
  });

  it('should have bin entry point', () => {
    const binPath = resolve(process.cwd(), 'bin', 'kritrima-ai.js');
    expect(existsSync(binPath)).toBe(true);
  });

  it('should have README file', () => {
    const readmePath = resolve(process.cwd(), 'README.md');
    expect(existsSync(readmePath)).toBe(true);
  });

  it('should have LICENSE file', () => {
    const licensePath = resolve(process.cwd(), 'LICENSE');
    expect(existsSync(licensePath)).toBe(true);
  });
});

describe('Package Configuration', () => {
  it('should have valid package.json structure', async () => {
    const packagePath = resolve(process.cwd(), 'package.json');
    const packageJson = await import(packagePath, { assert: { type: 'json' } });
    
    expect(packageJson.default.name).toBe('kritrima-ai-cli');
    expect(packageJson.default.version).toBe('1.1.0');
    expect(packageJson.default.type).toBe('module');
    expect(packageJson.default.bin).toHaveProperty('kritrima-ai');
    expect(packageJson.default.engines.node).toBe('>=22.0.0');
  });
});

describe('Environment', () => {
  it('should be running in test environment', () => {
    expect(process.env.NODE_ENV).toBe('test');
    expect(process.env.VITEST).toBe('true');
  });

  it('should have Node.js version 22 or higher', () => {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    expect(majorVersion).toBeGreaterThanOrEqual(22);
  });
});

describe('Test Utilities', () => {
  it('should have global test utilities available', () => {
    expect(globalThis.testUtils).toBeDefined();
    expect(typeof globalThis.testUtils.createTempDir).toBe('function');
    expect(typeof globalThis.testUtils.cleanupTempDir).toBe('function');
  });

  it('should be able to create and cleanup temp directories', () => {
    const tempDir = globalThis.testUtils.createTempDir();
    expect(existsSync(tempDir)).toBe(true);
    
    globalThis.testUtils.cleanupTempDir(tempDir);
    expect(existsSync(tempDir)).toBe(false);
  });
});
